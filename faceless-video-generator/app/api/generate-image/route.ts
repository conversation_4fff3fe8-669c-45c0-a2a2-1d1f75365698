import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { prompt } = await request.json();

    if (!prompt) {
      return NextResponse.json({ error: 'Prompt is required' }, { status: 400 });
    }

    // For demo purposes, we'll create a placeholder image
    // In production, you would use Hugging Face's free Stable Diffusion API
    // Example: https://huggingface.co/docs/api-inference/quicktour
    
    const imageUrl = await generatePlaceholderImage(prompt);
    
    return NextResponse.json({ imageUrl });
  } catch (error) {
    console.error('Error generating image:', error);
    return NextResponse.json({ error: 'Failed to generate image' }, { status: 500 });
  }
}

async function generatePlaceholderImage(prompt: string): Promise<string> {
  // Create a canvas-based placeholder image
  // In production, replace this with actual Hugging Face API call
  
  return new Promise((resolve) => {
    // This would be replaced with actual API call in production
    const canvas = document.createElement('canvas');
    canvas.width = 1280;
    canvas.height = 720;
    
    // For server-side, we'll return a data URL
    // In production, you'd call the Hugging Face API like this:
    /*
    const response = await fetch(
      "https://api-inference.huggingface.co/models/runwayml/stable-diffusion-v1-5",
      {
        headers: { Authorization: "Bearer YOUR_HF_TOKEN" },
        method: "POST",
        body: JSON.stringify({ inputs: prompt }),
      }
    );
    const blob = await response.blob();
    return URL.createObjectURL(blob);
    */
    
    // For now, return a placeholder
    resolve(`data:image/svg+xml,${encodeURIComponent(`
      <svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#a855f7;stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="100%" height="100%" fill="url(#grad)"/>
        <text x="640" y="300" font-family="Arial" font-size="48" fill="white" text-anchor="middle" font-weight="bold">AI Generated Image</text>
        <text x="640" y="400" font-family="Arial" font-size="24" fill="white" text-anchor="middle">${prompt.substring(0, 50)}...</text>
        <text x="640" y="450" font-family="Arial" font-size="16" fill="rgba(255,255,255,0.8)" text-anchor="middle">Demo Mode - Use Hugging Face API for real images</text>
      </svg>
    `)}`);
  });
}

// Alternative implementation using Hugging Face (commented out for demo)
/*
async function generateImageWithHuggingFace(prompt: string): Promise<string> {
  const HF_TOKEN = process.env.HUGGING_FACE_TOKEN; // Free token from huggingface.co
  
  if (!HF_TOKEN) {
    throw new Error('Hugging Face token not configured');
  }

  const response = await fetch(
    "https://api-inference.huggingface.co/models/runwayml/stable-diffusion-v1-5",
    {
      headers: { 
        Authorization: `Bearer ${HF_TOKEN}`,
        'Content-Type': 'application/json'
      },
      method: "POST",
      body: JSON.stringify({ 
        inputs: prompt,
        parameters: {
          width: 1280,
          height: 720,
          num_inference_steps: 20
        }
      }),
    }
  );

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const blob = await response.blob();
  const arrayBuffer = await blob.arrayBuffer();
  const base64 = Buffer.from(arrayBuffer).toString('base64');
  
  return `data:image/jpeg;base64,${base64}`;
}
*/
