# 🎬 Faceless Video Generator

A completely **FREE** web application that generates faceless YouTube videos from text prompts using AI-generated images. No API keys required, runs entirely in your browser!

## ✨ Features

- **100% Free**: No paid APIs or subscriptions required
- **Browser-Based**: All processing happens in your browser using WebAssembly
- **AI Image Generation**: Creates relevant images from text prompts (demo mode with placeholders)
- **Video Creation**: Combines images with transitions and effects
- **Text-to-Speech**: Optional narration support (demo mode)
- **Download Ready**: Export MP4 videos ready for YouTube upload

## 🚀 Quick Start

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Run Development Server**
   ```bash
   npm run dev
   ```

3. **Open in Browser**
   Navigate to `http://localhost:3000`

## 🎯 How to Use

1. **Enter Your Video Topic**: Describe what you want your video to be about
2. **Add Narration** (Optional): Enter text for voice-over
3. **Generate Images**: Click to create AI-generated images
4. **Create Video**: Combine images into a video
5. **Download**: Save your video as MP4

## 🛠 Technology Stack

- **Frontend**: Next.js 14 with TypeScript
- **Styling**: Tailwind CSS
- **Video Processing**: FFmpeg.wasm (runs in browser)
- **Image Generation**: SVG-based placeholders (ready for AI integration)
- **Icons**: Lucide React

## 🔧 Upgrading to Production

To use real AI services, you can integrate:

### Image Generation
- **Hugging Face** (Free tier available)
- **Stability AI**
- **OpenAI DALL-E**

### Text-to-Speech
- **ElevenLabs** (Free tier)
- **OpenAI TTS**
- **Google Cloud TTS**

## 📁 Project Structure

```
src/
├── app/
│   ├── api/generate-image/route.ts
│   ├── layout.tsx
│   └── page.tsx
└── components/
    └── VideoGenerator.tsx
```

## 🎨 Customization

- Modify video settings (duration, resolution, transitions)
- Update styling with Tailwind CSS
- Add custom animations and effects
- Integrate real AI services

## 💡 Use Cases

- **YouTube Content**: Create engaging videos without showing your face
- **Social Media**: Generate content for Instagram, TikTok, etc.
- **Educational Content**: Explain concepts with visual aids
- **Marketing**: Create promotional videos

---

**Made with ❤️ for content creators who want to make amazing videos without breaking the bank!**
