'use client';

import { useState, useRef, useEffect } from 'react';
import { FFmpeg } from '@ffmpeg/ffmpeg';
import { toBlobURL } from '@ffmpeg/util';
import { Play, Download, Loader2, Image as ImageIcon, Volume2 } from 'lucide-react';

interface GeneratedImage {
  url: string;
  prompt: string;
}

export default function VideoGenerator() {
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImages, setGeneratedImages] = useState<GeneratedImage[]>([]);
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  const [progress, setProgress] = useState('');
  const [ffmpegLoaded, setFfmpegLoaded] = useState(false);
  const [audioText, setAudioText] = useState('');
  const [videoSettings] = useState({
    duration: 3, // seconds per image
    transition: 'fade',
    resolution: '1280x720'
  });

  const ffmpegRef = useRef<FFmpeg | null>(null);

  useEffect(() => {
    loadFFmpeg();
  }, []);

  const loadFFmpeg = async () => {
    // Only load FFmpeg in the browser
    if (typeof window === 'undefined') return;

    try {
      const ffmpeg = new FFmpeg();
      ffmpegRef.current = ffmpeg;

      const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';

      await ffmpeg.load({
        coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
        wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
      });
      setFfmpegLoaded(true);
      setProgress('FFmpeg loaded successfully!');
    } catch (error) {
      console.error('Failed to load FFmpeg:', error);
      setProgress('Failed to load video processor. Please refresh the page.');
    }
  };

  const generateImages = async () => {
    if (!prompt.trim()) return;

    setIsGenerating(true);
    setProgress('Generating images from your prompt...');

    try {
      // Split prompt into multiple scenes for variety
      const scenes = [
        `${prompt} - wide shot`,
        `${prompt} - close up view`,
        `${prompt} - different angle`,
        `${prompt} - detailed view`
      ];

      const images: GeneratedImage[] = [];

      for (let i = 0; i < scenes.length; i++) {
        setProgress(`Generating image ${i + 1} of ${scenes.length}...`);

        // Use a free image generation service (placeholder for now)
        // In a real implementation, you'd use Hugging Face's free Stable Diffusion API
        const imageUrl = await generateImageFromPrompt(scenes[i]);

        images.push({
          url: imageUrl,
          prompt: scenes[i]
        });
      }

      setGeneratedImages(images);
      setProgress('Images generated successfully!');
    } catch (error) {
      console.error('Error generating images:', error);
      setProgress('Failed to generate images. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const generateImageFromPrompt = async (imagePrompt: string): Promise<string> => {
    // Create a placeholder image with SVG
    const colors = getColorsFromPrompt(imagePrompt);
    const svgContent = `
      <svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:${colors[0]};stop-opacity:1" />
            <stop offset="100%" style="stop-color:${colors[1]};stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="100%" height="100%" fill="url(#grad)"/>
        <text x="640" y="300" font-family="Arial" font-size="48" fill="white" text-anchor="middle" font-weight="bold">AI Generated Image</text>
        <text x="640" y="400" font-family="Arial" font-size="24" fill="white" text-anchor="middle">${imagePrompt.substring(0, 50)}...</text>
        <text x="640" y="450" font-family="Arial" font-size="16" fill="rgba(255,255,255,0.8)" text-anchor="middle">Demo Mode - Ready for real AI integration</text>
      </svg>
    `;

    return `data:image/svg+xml,${encodeURIComponent(svgContent)}`;
  };

  const getColorsFromPrompt = (prompt: string): [string, string] => {
    const colorMap: { [key: string]: [string, string] } = {
      'nature': ['#4ade80', '#22c55e'],
      'ocean': ['#06b6d4', '#0891b2'],
      'sunset': ['#f97316', '#ea580c'],
      'forest': ['#16a34a', '#15803d'],
      'city': ['#6366f1', '#4f46e5'],
      'space': ['#1e1b4b', '#312e81'],
      'fire': ['#dc2626', '#b91c1c'],
      'ice': ['#0ea5e9', '#0284c7']
    };

    for (const [keyword, colors] of Object.entries(colorMap)) {
      if (prompt.toLowerCase().includes(keyword)) {
        return colors;
      }
    }

    // Default gradient
    return ['#8b5cf6', '#a855f7'];
  };

  // Note: generateTextToSpeech function removed for demo - would be implemented in production

  const createVideo = async () => {
    if (!ffmpegLoaded || generatedImages.length === 0 || !ffmpegRef.current) {
      setProgress('Please generate images first and ensure FFmpeg is loaded.');
      return;
    }

    setIsGenerating(true);
    setProgress('Creating video...');

    try {
      const ffmpeg = ffmpegRef.current;

      // Write images to FFmpeg filesystem
      for (let i = 0; i < generatedImages.length; i++) {
        setProgress(`Processing image ${i + 1}...`);
        const response = await fetch(generatedImages[i].url);
        const imageData = await response.arrayBuffer();
        await ffmpeg.writeFile(`image${i}.png`, new Uint8Array(imageData));
      }

      // Generate audio if text is provided
      let audioCommand = '';
      if (audioText.trim()) {
        setProgress('Generating audio...');
        // Create a simple audio file (in production, use actual TTS)
        await ffmpeg.exec([
          '-f', 'lavfi',
          '-i', `sine=frequency=440:duration=${generatedImages.length * videoSettings.duration}`,
          '-ar', '44100',
          'audio.wav'
        ]);
        audioCommand = '-i audio.wav';
      }

      setProgress('Combining images into video...');

      // Create video from images with simpler approach
      const filterComplex = generatedImages.map((_, i) =>
        `[${i}:v]scale=1280:720:force_original_aspect_ratio=decrease,pad=1280:720:(ow-iw)/2:(oh-ih)/2,setpts=PTS-STARTPTS,fps=30[v${i}]`
      ).join(';') + ';' +
      generatedImages.map((_, i) => `[v${i}]`).join('') +
      `concat=n=${generatedImages.length}:v=1:a=0[outv]`;

      const ffmpegArgs = [
        '-y',
        ...generatedImages.flatMap((_, i) => ['-loop', '1', '-t', videoSettings.duration.toString(), '-i', `image${i}.png`]),
        ...(audioCommand ? ['-i', 'audio.wav'] : []),
        '-filter_complex', filterComplex,
        '-map', '[outv]',
        ...(audioCommand ? ['-map', `${generatedImages.length}:a`] : []),
        '-c:v', 'libx264',
        '-pix_fmt', 'yuv420p',
        '-shortest',
        'output.mp4'
      ];

      await ffmpeg.exec(ffmpegArgs);

      setProgress('Finalizing video...');
      const data = await ffmpeg.readFile('output.mp4');
      const videoBlob = new Blob([data], { type: 'video/mp4' });
      const url = URL.createObjectURL(videoBlob);

      setVideoUrl(url);
      setProgress('Video created successfully!');
    } catch (error) {
      console.error('Error creating video:', error);
      setProgress('Failed to create video. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadVideo = () => {
    if (videoUrl) {
      const a = document.createElement('a');
      a.href = videoUrl;
      a.download = 'faceless-video.mp4';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Input Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-semibold mb-4 flex items-center gap-2">
          <ImageIcon className="w-6 h-6" />
          Video Content
        </h2>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Video Topic/Prompt
            </label>
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="Describe what you want your video to be about... (e.g., 'A peaceful forest with sunlight streaming through trees')"
              className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              rows={3}
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2 flex items-center gap-2">
              <Volume2 className="w-4 h-4" />
              Narration Text (Optional)
            </label>
            <textarea
              value={audioText}
              onChange={(e) => setAudioText(e.target.value)}
              placeholder="Enter text for narration... (Note: This demo uses placeholder audio)"
              className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              rows={2}
            />
          </div>
        </div>

        <button
          onClick={generateImages}
          disabled={isGenerating || !prompt.trim()}
          className="mt-4 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg flex items-center gap-2 transition-colors"
        >
          {isGenerating ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <ImageIcon className="w-4 h-4" />
          )}
          Generate Images
        </button>
      </div>

      {/* Generated Images */}
      {generatedImages.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 className="text-xl font-semibold mb-4">Generated Images</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            {generatedImages.map((image, index) => (
              <div key={index} className="relative">
                {/* eslint-disable-next-line @next/next/no-img-element */}
                <img
                  src={image.url}
                  alt={`Generated image ${index + 1}`}
                  className="w-full h-32 object-cover rounded-lg"
                />
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-2 rounded-b-lg">
                  Scene {index + 1}
                </div>
              </div>
            ))}
          </div>

          <button
            onClick={createVideo}
            disabled={isGenerating || !ffmpegLoaded}
            className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg flex items-center gap-2 transition-colors"
          >
            {isGenerating ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Play className="w-4 h-4" />
            )}
            Create Video
          </button>
        </div>
      )}

      {/* Video Preview */}
      {videoUrl && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 className="text-xl font-semibold mb-4">Your Video</h3>
          <video
            src={videoUrl}
            controls
            className="w-full max-w-2xl mx-auto rounded-lg"
          />
          <div className="mt-4 text-center">
            <button
              onClick={downloadVideo}
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg flex items-center gap-2 mx-auto transition-colors"
            >
              <Download className="w-4 h-4" />
              Download Video
            </button>
          </div>
        </div>
      )}

      {/* Progress */}
      {progress && (
        <div className="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
          <p className="text-blue-800 dark:text-blue-200">{progress}</p>
        </div>
      )}

      {/* Instructions */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-3">How to Use</h3>
        <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600 dark:text-gray-300">
          <li>Enter a descriptive prompt for your video content</li>
          <li>Optionally add narration text</li>
          <li>Click &quot;Generate Images&quot; to create AI images</li>
          <li>Click &quot;Create Video&quot; to combine images into a video</li>
          <li>Download your finished video</li>
        </ol>
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-4">
          Note: This is a demo version. In production, it would use actual AI image generation APIs and text-to-speech services.
        </p>
      </div>
    </div>
  );
}
